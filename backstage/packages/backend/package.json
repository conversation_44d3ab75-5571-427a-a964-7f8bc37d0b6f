{"name": "backend", "version": "0.0.0", "main": "dist/index.cjs.js", "types": "src/index.ts", "private": true, "backstage": {"role": "backend"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "build-image": "docker build ../.. -f <PERSON><PERSON><PERSON><PERSON> --tag backstage"}, "dependencies": {"@backstage/backend-defaults": "^0.11.1", "@backstage/config": "^1.3.3", "@backstage/plugin-app-backend": "^0.5.4", "@backstage/plugin-auth-backend": "^0.25.2", "@backstage/plugin-auth-backend-module-github-provider": "^0.3.5", "@backstage/plugin-auth-backend-module-guest-provider": "^0.2.10", "@backstage/plugin-auth-node": "^0.6.5", "@backstage/plugin-catalog-backend": "^3.0.0", "@backstage/plugin-catalog-backend-module-logs": "^0.1.12", "@backstage/plugin-catalog-backend-module-scaffolder-entity-model": "^0.2.10", "@backstage/plugin-kubernetes-backend": "^0.19.8", "@backstage/plugin-permission-backend": "^0.7.2", "@backstage/plugin-permission-backend-module-allow-all-policy": "^0.2.10", "@backstage/plugin-permission-common": "^0.9.1", "@backstage/plugin-permission-node": "^0.10.2", "@backstage/plugin-proxy-backend": "^0.6.4", "@backstage/plugin-scaffolder-backend": "^2.1.0", "@backstage/plugin-scaffolder-backend-module-github": "^0.8.1", "@backstage/plugin-search-backend": "^2.0.4", "@backstage/plugin-search-backend-module-catalog": "^0.3.6", "@backstage/plugin-search-backend-module-pg": "^0.5.46", "@backstage/plugin-search-backend-module-techdocs": "^0.4.4", "@backstage/plugin-search-backend-node": "^1.3.13", "@backstage/plugin-techdocs-backend": "^2.0.4", "@internal/plugin-choreo-integration-backend": "workspace:^", "app": "link:../app", "better-sqlite3": "^9.0.0", "node-gyp": "^10.0.0", "pg": "^8.11.3"}, "devDependencies": {"@backstage/cli": "^0.33.1"}, "files": ["dist"]}