import {
  createPlugin,
  createRoutableExtension,
} from '@backstage/core-plugin-api';

import { rootRouteRef } from './routes';

export const choreoIntegrationPlugin = createPlugin({
  id: 'choreo-integration',
  routes: {
    root: rootRouteRef,
  },
});

export const ChoreoIntegrationPage = choreoIntegrationPlugin.provide(
  createRoutableExtension({
    name: 'ChoreoIntegrationPage',
    component: () =>
      import('./components/ExampleComponent').then(m => m.ExampleComponent),
    mountPoint: rootRouteRef,
  }),
);
